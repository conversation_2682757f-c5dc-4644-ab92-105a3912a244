/* eslint-disable tailwindcss/no-custom-classname */

'use client';

import Script from 'next/script';
import { useEffect, useInsertionEffect, useState } from 'react';

import { useAppSelector } from 'store/hooks';
import { ServiceType } from 'types/Story';

import type { GenericElement } from 'types/Story';

interface LiveBlogPost {
  articleBody: string | null;
  datePublished: string;
  headline: string | null;
}

interface LiveBlogPostsResponse {
  result: LiveBlogPost[];
  success: boolean;
}

interface Props {
  element: GenericElement;
}

function NorkonLiveCenter({ element }: Props): React.ReactElement | null {
  const [liveBlogPosts, setLiveBlogPosts] = useState<LiveBlogPost[]>([]);
  const hasPianoPaywall = useAppSelector(
    (state) =>
      state.features.piano.enabled &&
      (state.piano.hasPaywall || state.piano.loadingPaywall),
  );
  const norkonFeature = useAppSelector(
    (state) => state.features.norkonLiveblog,
  );
  const host = useAppSelector((state) => state.settings.host);
  const story = useAppSelector((state) => state.story);
  const { serviceId } = element;

  // Fetch live blog posts for structured data
  useEffect(() => {
    if (!norkonFeature.enabled || !norkonFeature.data?.tenantKey || !serviceId)
      return () => {};

    const { apiUrl, tenantKey } = norkonFeature.data;
    // eslint-disable-next-line compat/compat
    const controller = new AbortController();

    fetch(`${apiUrl}/api/v2/metadata/${tenantKey}/${serviceId}/posts`, {
      signal: controller.signal,
    })
      .then((response) => response.json())
      .then((data: LiveBlogPostsResponse) => {
        if (data.success && Array.isArray(data.result)) {
          setLiveBlogPosts(data.result);
        } else {
          console.error('Unexpected response shape:', data);
          setLiveBlogPosts([]);
        }
      })
      .catch((error) => {
        console.error('Failed to fetch live blog posts:', error);
      });

    return () => {
      controller?.abort();
    };
  }, [norkonFeature, serviceId]);

  useInsertionEffect(() => {
    if (norkonFeature.enabled) {
      const { assetVersion, baseUrl, tenantKey } = norkonFeature.data;

      // eslint-disable-next-line @stylistic/max-len
      const baseCssUrl = `${baseUrl}scripts/ncposts/ncposts-${assetVersion}.min.css`;
      const baseCssLink = document.createElement('link');
      baseCssLink.href = baseCssUrl;
      baseCssLink.rel = 'stylesheet';
      document.head.appendChild(baseCssLink);

      const extensionCssUrl = `${baseUrl}LiveCenter/ExtensionCss/${tenantKey}`;
      const extensionCssLink = document.createElement('link');
      extensionCssLink.href = extensionCssUrl;
      extensionCssLink.rel = 'stylesheet';
      document.head.appendChild(extensionCssLink);

      return () => {
        document.head.removeChild(baseCssLink);
        document.head.removeChild(extensionCssLink);
      };
    }

    return () => {};
  }, [norkonFeature]);

  if (
    element.serviceType !== ServiceType.Blog ||
    hasPianoPaywall ||
    !norkonFeature.enabled
  ) {
    return null;
  }



  // Build structured data
  const absoluteCanonicalUrl = story.canonicalUrl?.startsWith('/')
    ? `https://${host}${story.canonicalUrl}`
    : story.canonicalUrl;

  const structuredData = {
    '@context': 'https://schema.org',
    '@id': absoluteCanonicalUrl,
    '@type': 'LiveBlogPosting',
    about: {
      '@type': 'Event',
      description: element.description,
      eventAttendanceMode: 'https://schema.org/OnlineEventAttendanceMode',
      location: {
        '@type': 'VirtualLocation',
        url: absoluteCanonicalUrl,
      },
      name: story.title || element.description,
      organizer: {
        '@type': 'Person',
        name: story.byline,
        url: absoluteCanonicalUrl,
      },
      startDate: story.publishFrom,
    },
    coverageStartTime: story.publishFrom,
    description: story.summary,
    headline: story.title || element.description,
    liveBlogUpdate: liveBlogPosts.map((post) => ({
      '@type': 'BlogPosting',
      articleBody: post.articleBody || '',
      datePublished: post.datePublished,
      headline: post.headline,
    })),
  };

  return (
    <>
      <script
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData),
        }}
        type="application/ld+json"
      />
      <div className="lc-frame lc-default-theme font-opensans">
        <div className="lc-feed-container">
          <div id="master-container" />
          <div className="text-center">
            <button className="lc-load-more" id="lc-load-more" type="button">
              Load more
            </button>
          </div>
        </div>
      </div>
      <Script
        async
        id="norkon-live-center"
        src={`${scriptUrl}scripts/ncposts/ncposts-${assetVersion}.min.js`}
      />
      <Script
        async
        onLoad={() => {
          window.NcPosts.start({
            baseUrl,
            channelId: serviceId,
            container: document.getElementById('master-container'),
            extensionContainer: window.NcLiveCenterExtensions,
            showMoreElement: document.getElementById('lc-load-more'),
            tenantKey,
            wsBaseUrl: websocketUrl,
          });
        }}
        src={`${scriptUrl}LiveCenter/ExtensionJs/${tenantKey}`}
      />
    </>
  );
}

export default NorkonLiveCenter;
