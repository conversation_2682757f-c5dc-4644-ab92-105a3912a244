'use client';

import { useEffect, useState } from 'react';

import { useAppSelector } from 'store/hooks';

import type { Feature, NorkonLiveblogFeature } from 'store/slices/features';
import type { GenericElement } from 'types/Story';

export interface LiveBlogPost {
  articleBody: string | null;
  datePublished: string;
  headline: string | null;
}

interface LiveBlogPostsResponse {
  result: LiveBlogPost[];
  success: boolean;
}

export function useStructuredMarkup(
  element: GenericElement,
  norkonFeature: Feature<NorkonLiveblogFeature>,
  serviceId: string,
): object {
  const [liveBlogPosts, setLiveBlogPosts] = useState<LiveBlogPost[]>([]);

  const host = useAppSelector((state) => state.settings.host);
  const story = useAppSelector((state) => state.story);

  // Fetch live blog posts for structured data
  useEffect(() => {
    if (
      !norkonFeature.enabled ||
      !norkonFeature.data?.tenantKey ||
      !serviceId
    ) {
      return () => {};  
    }

    const { apiUrl, tenantKey } = norkonFeature.data;
    // eslint-disable-next-line compat/compat
    const controller = new AbortController();

    fetch(`${apiUrl}/api/v2/metadata/${tenantKey}/${serviceId}/posts`, {
      signal: controller.signal,
    })
      .then((response) => response.json())
      .then((data: LiveBlogPostsResponse) => {
        if (data.success && Array.isArray(data.result)) {
          setLiveBlogPosts(data.result);
        } else {
          console.error('Unexpected response shape:', data);
          setLiveBlogPosts([]);
        }
      })
      .catch((error) => {
        console.error('Failed to fetch live blog posts:', error);
      });


    return () => {
      controller?.abort();
    };
  }, [norkonFeature, serviceId]);

  // Build structured data
  const absoluteCanonicalUrl = story.canonicalUrl?.startsWith('/')
    ? `https://${host}${story.canonicalUrl}`
    : story.canonicalUrl;

  const structuredData = {
    '@context': 'https://schema.org',
    '@id': absoluteCanonicalUrl,
    '@type': 'LiveBlogPosting',
    about: {
      '@type': 'Event',
      description: element.description,
      eventAttendanceMode: 'https://schema.org/OnlineEventAttendanceMode',
      location: {
        '@type': 'VirtualLocation',
        url: absoluteCanonicalUrl,
      },
      name: story.title || element.description,
      organizer: {
        '@type': 'Person',
        name: story.byline,
        url: absoluteCanonicalUrl,
      },
      startDate: story.publishFrom,
    },
    coverageStartTime: story.publishFrom,
    description: story.summary,
    headline: story.title || element.description,
    liveBlogUpdate: liveBlogPosts.map((post) => ({
      '@type': 'BlogPosting',
      articleBody: post.articleBody || '',
      datePublished: post.datePublished,
      headline: post.headline,
    })),
  };

  return structuredData;
}
